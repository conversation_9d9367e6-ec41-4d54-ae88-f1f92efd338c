import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

import '../database/database_manager.dart';
import '../models/drift_event.dart';
import '../models/drift_node.dart';
import 'tailscale_client.dart';

/// Manages synchronization between Drift nodes
class SyncManager {
  final DatabaseManager _databaseManager;
  final String _nodeId;
  final int _apiPort;
  final TailscaleClient? _tailscaleClient;
  
  Timer? _syncTimer;
  List<DriftNode> _knownNodes = [];
  bool _isInitialized = false;
  
  static const Duration _syncInterval = Duration(seconds: 15);
  static const Duration _nodeDiscoveryInterval = Duration(minutes: 1);
  
  SyncManager({
    required DatabaseManager databaseManager,
    required String nodeId,
    required int apiPort,
    String? tailscaleAccessToken,
    String? tailnet,
  }) : _databaseManager = databaseManager,
       _nodeId = nodeId,
       _apiPort = apiPort,
       _tailscaleClient = tailscaleAccessToken != null
           ? TailscaleClient(
               accessToken: tailscaleAccessToken,
               tailnet: tailnet ?? TailscaleClient.extractTailnet(tailscaleAccessToken, tailnet),
             )
           : null;

  /// Initialize the sync manager
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Test Tailscale connection if configured
    if (_tailscaleClient != null) {
      final connected = await _tailscaleClient!.testConnection();
      if (connected) {
        print('Tailscale API connection successful');
        await _discoverNodes();
      } else {
        print('Warning: Tailscale API connection failed');
      }
    } else {
      print('Tailscale not configured, running in local mode');
    }
    
    // Start periodic sync
    _startPeriodicSync();
    
    _isInitialized = true;
  }

  /// Shutdown the sync manager
  Future<void> shutdown() async {
    _syncTimer?.cancel();
    _syncTimer = null;
    _isInitialized = false;
  }

  /// Start periodic synchronization
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(_syncInterval, (_) async {
      await _performSync();
    });
    
    // Also perform node discovery periodically
    Timer.periodic(_nodeDiscoveryInterval, (_) async {
      await _discoverNodes();
    });
  }

  /// Discover nodes using Tailscale API
  Future<void> _discoverNodes() async {
    if (_tailscaleClient == null) return;
    
    try {
      final nodes = await _tailscaleClient!.getDevices(defaultPort: _apiPort);
      
      // Filter out this node
      _knownNodes = nodes.where((node) => node.id != _nodeId).toList();
      
      print('Discovered ${_knownNodes.length} nodes: ${_knownNodes.map((n) => n.name).join(', ')}');
    } catch (e) {
      print('Failed to discover nodes: $e');
    }
  }

  /// Perform synchronization with all known nodes
  Future<void> _performSync() async {
    if (_knownNodes.isEmpty) return;
    
    try {
      // Get unsynced events to send
      final unsyncedEvents = await _databaseManager.getUnsyncedEvents();
      
      final List<String> syncedEventIds = [];
      
      for (final node in _knownNodes) {
        try {
          // Send events to node
          if (unsyncedEvents.isNotEmpty) {
            await _sendEventsToNode(node, unsyncedEvents);
            syncedEventIds.addAll(unsyncedEvents.map((e) => e.id));
          }
          
          // Receive events from node
          await _receiveEventsFromNode(node);
          
        } catch (e) {
          print('Failed to sync with node ${node.name}: $e');
        }
      }
      
      // Mark successfully sent events as synced
      if (syncedEventIds.isNotEmpty) {
        await _databaseManager.markEventsSynced(syncedEventIds);
      }
      
    } catch (e) {
      print('Sync error: $e');
    }
  }

  /// Send events to a specific node
  Future<void> _sendEventsToNode(DriftNode node, List<DriftEvent> events) async {
    final url = '${node.baseUrl}/events';
    
    final response = await http.post(
      Uri.parse(url),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'events': events.map((e) => e.toJson()).toList(),
        'sender_node_id': _nodeId,
      }),
    ).timeout(const Duration(seconds: 10));
    
    if (response.statusCode != 200) {
      throw Exception('Failed to send events to ${node.name}: ${response.statusCode}');
    }
  }

  /// Receive events from a specific node
  Future<void> _receiveEventsFromNode(DriftNode node) async {
    final url = '${node.baseUrl}/events';
    
    final response = await http.get(
      Uri.parse(url),
      headers: {'Content-Type': 'application/json'},
    ).timeout(const Duration(seconds: 10));
    
    if (response.statusCode != 200) {
      throw Exception('Failed to receive events from ${node.name}: ${response.statusCode}');
    }
    
    final data = jsonDecode(response.body) as Map<String, dynamic>;
    final eventsData = data['events'] as List<dynamic>;
    
    if (eventsData.isNotEmpty) {
      final events = eventsData
          .map((eventData) => DriftEvent.fromJson(eventData as Map<String, dynamic>))
          .toList();
      
      // Filter out events that originated from this node
      final externalEvents = events.where((event) => event.origin != _nodeId).toList();
      
      if (externalEvents.isNotEmpty) {
        await _databaseManager.insertEvents(externalEvents);
        print('Received ${externalEvents.length} events from ${node.name}');
      }
    }
  }

  /// Manually trigger synchronization
  Future<void> syncNow() async {
    await _performSync();
  }

  /// Add a node manually (for testing or local network discovery)
  void addNode(DriftNode node) {
    if (!_knownNodes.any((n) => n.id == node.id)) {
      _knownNodes.add(node);
    }
  }

  /// Remove a node
  void removeNode(String nodeId) {
    _knownNodes.removeWhere((node) => node.id == nodeId);
  }

  /// Get list of known nodes
  List<DriftNode> get knownNodes => List.unmodifiable(_knownNodes);

  /// Check if a node is reachable
  Future<bool> isNodeReachable(DriftNode node) async {
    try {
      final response = await http.get(
        Uri.parse('${node.baseUrl}/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
