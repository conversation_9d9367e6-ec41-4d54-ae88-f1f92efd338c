import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:drift_core/drift_core.dart';

import 'screens/task_list_screen.dart';
import 'providers/drift_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Drift core
  final driftCore = DriftCore();
  await driftCore.initialize(
    // You can configure Tailscale here if needed
    // tailscaleAccessToken: 'your-token-here',
  );
  
  runApp(DriftApp(driftCore: driftCore));
}

class DriftApp extends StatelessWidget {
  final DriftCore driftCore;
  
  const DriftApp({super.key, required this.driftCore});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => DriftProvider(driftCore),
      child: MaterialApp(
        title: 'Drift - Distributed Tasks',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const TaskListScreen(),
      ),
    );
  }
}
