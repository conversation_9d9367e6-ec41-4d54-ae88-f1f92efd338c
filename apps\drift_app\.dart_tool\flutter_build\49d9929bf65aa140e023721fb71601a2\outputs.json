["D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\code\\drift\\apps\\drift_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]