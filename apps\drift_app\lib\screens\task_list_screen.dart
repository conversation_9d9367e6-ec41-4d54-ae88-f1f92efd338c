import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/drift_provider.dart';
import '../widgets/task_item.dart';
import '../widgets/add_task_dialog.dart';

/// Main screen showing the list of tasks
class TaskListScreen extends StatelessWidget {
  const TaskListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Drift Tasks'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer<DriftProvider>(
            builder: (context, provider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Node info
                  Tooltip(
                    message: 'Node ID: ${provider.nodeId}',
                    child: Icon(
                      Icons.device_hub,
                      color: provider.knownNodesCount > 0 
                          ? Colors.green 
                          : Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${provider.knownNodesCount}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16),
                  // Refresh button
                  IconButton(
                    onPressed: provider.isLoading ? null : () {
                      provider.refresh();
                    },
                    icon: provider.isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.refresh),
                    tooltip: 'Sync with other nodes',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<DriftProvider>(
        builder: (context, provider, child) {
          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${provider.error}',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.refresh(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (provider.tasks.isEmpty && !provider.isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.task_alt,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No tasks yet',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Tap the + button to create your first task',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: provider.refresh,
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: provider.tasks.length,
              itemBuilder: (context, index) {
                final task = provider.tasks[index];
                return TaskItem(
                  task: task,
                  onCompleted: () => provider.completeTask(task.id),
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: Consumer<DriftProvider>(
        builder: (context, provider, child) {
          return FloatingActionButton(
            onPressed: provider.isLoading ? null : () {
              _showAddTaskDialog(context);
            },
            tooltip: 'Add Task',
            child: const Icon(Icons.add),
          );
        },
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddTaskDialog(),
    );
  }
}
