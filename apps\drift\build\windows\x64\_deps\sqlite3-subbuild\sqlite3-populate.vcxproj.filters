﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\51def7b3cb639b367c87d70e58c50d27\sqlite3-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\3ada72338708bb178ac726b7161c3e97\sqlite3-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\4e20c601927d494ee20869b2fb41d605\sqlite3-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\code\drift\apps\drift\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\sqlite3-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{3F8DD4DF-797D-3629-9DDB-DABEF7F9EFAF}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
