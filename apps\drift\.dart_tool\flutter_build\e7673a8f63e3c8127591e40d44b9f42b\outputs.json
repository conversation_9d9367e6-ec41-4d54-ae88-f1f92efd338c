["D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_windows.dll", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_export.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_messenger.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\flutter_windows.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\icudtl.dat", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "D:\\code\\drift\\apps\\drift\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h", "D:\\code\\drift\\apps\\drift\\build\\windows\\app.so", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\AssetManifest.json", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\AssetManifest.bin", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\FontManifest.json", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\NOTICES.Z", "D:\\code\\drift\\apps\\drift\\build\\flutter_assets\\NativeAssetsManifest.json"]