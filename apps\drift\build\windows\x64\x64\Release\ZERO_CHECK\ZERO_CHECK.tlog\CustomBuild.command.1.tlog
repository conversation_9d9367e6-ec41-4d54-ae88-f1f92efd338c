^D:\CODE\DRIFT\APPS\DRIFT\BUILD\WINDOWS\X64\CMAKEFILES\363209B27C919F54D9DA1226A66D79B5\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/drift/windows -BD:/code/drift/apps/drift/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/drift/apps/drift/build/windows/x64/drift.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
