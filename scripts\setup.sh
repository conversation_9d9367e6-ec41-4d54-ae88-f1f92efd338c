#!/bin/bash

# Drift Project Setup Script
# This script sets up the development environment for the Drift project

set -e

echo "🚀 Setting up Drift project..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    echo "Visit: https://docs.flutter.dev/get-started/install"
    exit 1
fi

echo "✅ Flutter found: $(flutter --version | head -n 1)"

# Navigate to project root
cd "$(dirname "$0")/.."

# Get dependencies for core package
echo "📦 Getting dependencies for core package..."
cd apps/core
flutter pub get
cd ../..

# Get dependencies for Flutter app
echo "📦 Getting dependencies for Flutter app..."
cd apps/drift_app
flutter pub get
cd ../..

# Check Flutter doctor
echo "🔍 Running Flutter doctor..."
flutter doctor

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p shared/models
mkdir -p shared/utils

# Set executable permissions for scripts
echo "🔧 Setting executable permissions..."
chmod +x scripts/*.sh

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Configure Tailscale access token (optional)"
echo "2. Run the Flutter app: cd apps/drift_app && flutter run"
echo "3. Test the core package: cd apps/core && flutter test"
echo ""
echo "For more information, see the README.md file."
