import 'dart:convert';

/// Represents an event in the Drift system
class DriftEvent {
  final String id;
  final String origin;
  final DateTime timestamp;
  final String entityType;
  final String entityId;
  final String eventType;
  final Map<String, dynamic> payload;
  final bool synced;

  const DriftEvent({
    required this.id,
    required this.origin,
    required this.timestamp,
    required this.entityType,
    required this.entityId,
    required this.eventType,
    required this.payload,
    this.synced = false,
  });

  /// Create a DriftEvent from a database row
  factory DriftEvent.fromMap(Map<String, dynamic> map) {
    return DriftEvent(
      id: map['id'] as String,
      origin: map['origin'] as String,
      timestamp: DateTime.parse(map['timestamp'] as String),
      entityType: map['entity_type'] as String,
      entityId: map['entity_id'] as String,
      eventType: map['event_type'] as String,
      payload: jsonDecode(map['payload'] as String) as Map<String, dynamic>,
      synced: (map['synced'] as int) == 1,
    );
  }

  /// Convert the event to a database row
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'origin': origin,
      'timestamp': timestamp.toIso8601String(),
      'entity_type': entityType,
      'entity_id': entityId,
      'event_type': eventType,
      'payload': jsonEncode(payload),
      'synced': synced ? 1 : 0,
    };
  }

  /// Create a copy with updated fields
  DriftEvent copyWith({
    String? id,
    String? origin,
    DateTime? timestamp,
    String? entityType,
    String? entityId,
    String? eventType,
    Map<String, dynamic>? payload,
    bool? synced,
  }) {
    return DriftEvent(
      id: id ?? this.id,
      origin: origin ?? this.origin,
      timestamp: timestamp ?? this.timestamp,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      eventType: eventType ?? this.eventType,
      payload: payload ?? this.payload,
      synced: synced ?? this.synced,
    );
  }

  /// Convert to JSON for API transmission
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'origin': origin,
      'timestamp': timestamp.toIso8601String(),
      'entity_type': entityType,
      'entity_id': entityId,
      'event_type': eventType,
      'payload': payload,
      'synced': synced,
    };
  }

  /// Create from JSON received from API
  factory DriftEvent.fromJson(Map<String, dynamic> json) {
    return DriftEvent(
      id: json['id'] as String,
      origin: json['origin'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      entityType: json['entity_type'] as String,
      entityId: json['entity_id'] as String,
      eventType: json['event_type'] as String,
      payload: json['payload'] as Map<String, dynamic>,
      synced: json['synced'] as bool? ?? false,
    );
  }

  @override
  String toString() {
    return 'DriftEvent(id: $id, origin: $origin, timestamp: $timestamp, '
        'entityType: $entityType, entityId: $entityId, eventType: $eventType, '
        'payload: $payload, synced: $synced)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DriftEvent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
