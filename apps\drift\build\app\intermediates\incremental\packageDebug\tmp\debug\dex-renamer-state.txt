#Sun Jul 06 20:28:34 PET 2025
base.0=D\:\\code\\drift\\apps\\drift\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=D\:\\code\\drift\\apps\\drift\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.2=D\:\\code\\drift\\apps\\drift\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.3=D\:\\code\\drift\\apps\\drift\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.4=D\:\\code\\drift\\apps\\drift\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=0/classes.dex
path.3=15/classes.dex
path.4=1/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
