^D:\CODE\DRIFT\APPS\DRIFT_APP\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/drift_app/windows -BD:/code/drift/apps/drift_app/build/windows/x64 --check-stamp-file D:/code/drift/apps/drift_app/build/windows/x64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
