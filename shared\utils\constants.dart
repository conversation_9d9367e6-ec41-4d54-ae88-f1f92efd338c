/// Shared constants for the Drift system

class DriftConstants {
  // API Configuration
  static const int defaultApiPort = 8080;
  static const Duration syncInterval = Duration(seconds: 15);
  static const Duration nodeDiscoveryInterval = Duration(minutes: 1);
  static const Duration apiTimeout = Duration(seconds: 10);
  
  // Database Configuration
  static const String databaseName = 'drift.db';
  static const int databaseVersion = 1;
  
  // Event Types
  static const String eventTypeTaskCreated = 'task_created';
  static const String eventTypeTaskUpdated = 'task_updated';
  static const String eventTypeTaskCompleted = 'task_completed';
  static const String eventTypeTaskDeleted = 'task_deleted';
  
  // Entity Types
  static const String entityTypeTask = 'task';
  
  // File Names
  static const String nodeIdFileName = 'drift_node_id.txt';
  
  // API Endpoints
  static const String endpointHealth = '/health';
  static const String endpointNode = '/node';
  static const String endpointEvents = '/events';
  static const String endpointDatabase = '/database';
  static const String endpointTasks = '/tasks';
  
  // Tailscale Configuration
  static const String tailscaleApiBaseUrl = 'https://api.tailscale.com/api/v2';
  
  // Error Messages
  static const String errorDatabaseNotInitialized = 'Database not initialized. Call initialize() first.';
  static const String errorTailscaleNotConfigured = 'Tailscale access token and tailnet must be configured';
  static const String errorNodeNotReachable = 'Node is not reachable';
  
  // Success Messages
  static const String successEventsSynced = 'Events synced successfully';
  static const String successTaskCreated = 'Task created successfully';
  static const String successTaskUpdated = 'Task updated successfully';
}
