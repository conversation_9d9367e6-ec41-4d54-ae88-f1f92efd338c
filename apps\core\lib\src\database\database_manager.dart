import 'dart:async';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sqlite3/sqlite3.dart';

import '../models/drift_event.dart';

/// Manages the SQLite database and migrations
class DatabaseManager {
  static const String _databaseName = 'drift.db';

  Database? _database;

  /// Get the database instance
  Database get database {
    if (_database == null) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  /// Initialize the database
  Future<void> initialize() async {
    if (_database != null) return;

    final directory = await getApplicationDocumentsDirectory();
    final dbPath = path.join(directory.path, _databaseName);

    _database = sqlite3.open(dbPath);

    // Run migrations
    _runMigrations();
  }

  /// Run database migrations
  void _runMigrations() {
    // Check if events table exists
    final result = database.select(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='events'"
    );

    if (result.isEmpty) {
      // Create initial schema
      database.execute('''
        CREATE TABLE events (
          id TEXT PRIMARY KEY,
          origin TEXT NOT NULL,
          timestamp TEXT NOT NULL,
          entity_type TEXT NOT NULL,
          entity_id TEXT NOT NULL,
          event_type TEXT NOT NULL,
          payload TEXT NOT NULL,
          synced INTEGER DEFAULT 0
        )
      ''');

      // Create indexes
      database.execute('CREATE INDEX idx_events_entity ON events(entity_type, entity_id)');
      database.execute('CREATE INDEX idx_events_timestamp ON events(timestamp)');
      database.execute('CREATE INDEX idx_events_synced ON events(synced)');
    }
  }

  /// Insert a new event
  Future<void> insertEvent(DriftEvent event) async {
    final stmt = database.prepare('''
      INSERT OR REPLACE INTO events
      (id, origin, timestamp, entity_type, entity_id, event_type, payload, synced)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''');

    final eventMap = event.toMap();
    stmt.execute([
      eventMap['id'],
      eventMap['origin'],
      eventMap['timestamp'],
      eventMap['entity_type'],
      eventMap['entity_id'],
      eventMap['event_type'],
      eventMap['payload'],
      eventMap['synced'],
    ]);

    stmt.dispose();
  }

  /// Insert multiple events in a transaction
  Future<void> insertEvents(List<DriftEvent> events) async {
    database.execute('BEGIN TRANSACTION');

    try {
      final stmt = database.prepare('''
        INSERT OR REPLACE INTO events
        (id, origin, timestamp, entity_type, entity_id, event_type, payload, synced)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      ''');

      for (final event in events) {
        final eventMap = event.toMap();
        stmt.execute([
          eventMap['id'],
          eventMap['origin'],
          eventMap['timestamp'],
          eventMap['entity_type'],
          eventMap['entity_id'],
          eventMap['event_type'],
          eventMap['payload'],
          eventMap['synced'],
        ]);
      }

      stmt.dispose();
      database.execute('COMMIT');
    } catch (e) {
      database.execute('ROLLBACK');
      rethrow;
    }
  }

  /// Get all unsynced events
  Future<List<DriftEvent>> getUnsyncedEvents() async {
    final result = database.select(
      'SELECT * FROM events WHERE synced = 0 ORDER BY timestamp ASC'
    );
    return result.map((row) => DriftEvent.fromMap(row)).toList();
  }

  /// Get events for a specific entity
  Future<List<DriftEvent>> getEventsForEntity(String entityType, String entityId) async {
    final stmt = database.prepare(
      'SELECT * FROM events WHERE entity_type = ? AND entity_id = ? ORDER BY timestamp ASC'
    );

    final result = stmt.select([entityType, entityId]);
    stmt.dispose();

    return result.map((row) => DriftEvent.fromMap(row)).toList();
  }

  /// Mark events as synced
  Future<void> markEventsSynced(List<String> eventIds) async {
    if (eventIds.isEmpty) return;

    database.execute('BEGIN TRANSACTION');

    try {
      final stmt = database.prepare('UPDATE events SET synced = 1 WHERE id = ?');

      for (final eventId in eventIds) {
        stmt.execute([eventId]);
      }

      stmt.dispose();
      database.execute('COMMIT');
    } catch (e) {
      database.execute('ROLLBACK');
      rethrow;
    }
  }

  /// Get all events (for debugging/export)
  Future<List<DriftEvent>> getAllEvents() async {
    final result = database.select('SELECT * FROM events ORDER BY timestamp ASC');
    return result.map((row) => DriftEvent.fromMap(row)).toList();
  }

  /// Get database file path for sharing
  Future<String> getDatabasePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return path.join(directory.path, _databaseName);
  }

  /// Close the database
  Future<void> close() async {
    _database?.dispose();
    _database = null;
  }
}
