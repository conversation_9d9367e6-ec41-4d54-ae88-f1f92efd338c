import 'dart:async';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import '../models/drift_event.dart';

/// Manages the SQLite database and migrations
class DatabaseManager {
  static const String _databaseName = 'drift.db';
  static const int _databaseVersion = 1;
  
  Database? _database;
  
  /// Get the database instance
  Database get database {
    if (_database == null) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  /// Initialize the database
  Future<void> initialize() async {
    if (_database != null) return;

    // Initialize sqflite_ffi for desktop platforms
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    final directory = await getApplicationDocumentsDirectory();
    final dbPath = path.join(directory.path, _databaseName);

    _database = await openDatabase(
      dbPath,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    await _runMigration(db, 1);
  }

  /// Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    for (int version = oldVersion + 1; version <= newVersion; version++) {
      await _runMigration(db, version);
    }
  }

  /// Run a specific migration
  Future<void> _runMigration(Database db, int version) async {
    switch (version) {
      case 1:
        await _createEventsTable(db);
        break;
      default:
        throw ArgumentError('Unknown migration version: $version');
    }
  }

  /// Create the events table (Migration v1)
  Future<void> _createEventsTable(Database db) async {
    await db.execute('''
      CREATE TABLE events (
        id TEXT PRIMARY KEY,
        origin TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        payload TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      )
    ''');
    
    // Create indexes for better query performance
    await db.execute('CREATE INDEX idx_events_origin ON events(origin)');
    await db.execute('CREATE INDEX idx_events_entity ON events(entity_type, entity_id)');
    await db.execute('CREATE INDEX idx_events_synced ON events(synced)');
    await db.execute('CREATE INDEX idx_events_timestamp ON events(timestamp)');
  }

  /// Insert a new event
  Future<void> insertEvent(DriftEvent event) async {
    await database.insert(
      'events',
      event.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Insert multiple events in a transaction
  Future<void> insertEvents(List<DriftEvent> events) async {
    final batch = database.batch();
    for (final event in events) {
      batch.insert(
        'events',
        event.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  /// Get all unsynced events
  Future<List<DriftEvent>> getUnsyncedEvents() async {
    final List<Map<String, dynamic>> maps = await database.query(
      'events',
      where: 'synced = ?',
      whereArgs: [0],
      orderBy: 'timestamp ASC',
    );
    
    return maps.map((map) => DriftEvent.fromMap(map)).toList();
  }

  /// Get events for a specific entity
  Future<List<DriftEvent>> getEventsForEntity(String entityType, String entityId) async {
    final List<Map<String, dynamic>> maps = await database.query(
      'events',
      where: 'entity_type = ? AND entity_id = ?',
      whereArgs: [entityType, entityId],
      orderBy: 'timestamp ASC',
    );
    
    return maps.map((map) => DriftEvent.fromMap(map)).toList();
  }

  /// Mark events as synced
  Future<void> markEventsSynced(List<String> eventIds) async {
    if (eventIds.isEmpty) return;
    
    final batch = database.batch();
    for (final eventId in eventIds) {
      batch.update(
        'events',
        {'synced': 1},
        where: 'id = ?',
        whereArgs: [eventId],
      );
    }
    await batch.commit();
  }

  /// Get all events (for debugging/export)
  Future<List<DriftEvent>> getAllEvents() async {
    final List<Map<String, dynamic>> maps = await database.query(
      'events',
      orderBy: 'timestamp ASC',
    );
    
    return maps.map((map) => DriftEvent.fromMap(map)).toList();
  }

  /// Get database file path for sharing
  Future<String> getDatabasePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return path.join(directory.path, _databaseName);
  }

  /// Close the database
  Future<void> close() async {
    await _database?.close();
    _database = null;
  }
}
