-- Migration v1: Initial database schema
-- Creates the events table for event sourcing

CREATE TABLE events (
  id TEXT PRIMARY KEY,
  origin TEXT NOT NULL,
  timestamp TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  event_type TEXT NOT NULL,
  payload TEXT NOT NULL,
  synced INTEGER DEFAULT 0
);

-- <PERSON>reate indexes for better query performance
CREATE INDEX idx_events_origin ON events(origin);
CREATE INDEX idx_events_entity ON events(entity_type, entity_id);
CREATE INDEX idx_events_synced ON events(synced);
CREATE INDEX idx_events_timestamp ON events(timestamp);

-- Insert migration record
INSERT INTO schema_migrations (version, applied_at) VALUES (1, datetime('now'))
  ON CONFLICT(version) DO NOTHING;

-- Create schema_migrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
  version INTEGER PRIMARY KEY,
  applied_at TEXT NOT NULL
);
