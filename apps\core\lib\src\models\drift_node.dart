/// Represents a node in the Drift network
class DriftNode {
  final String id;
  final String name;
  final String ipAddress;
  final int port;
  final bool isOnline;
  final DateTime lastSeen;

  const DriftNode({
    required this.id,
    required this.name,
    required this.ipAddress,
    required this.port,
    required this.isOnline,
    required this.lastSeen,
  });

  /// Create a DriftNode from Tailscale API response
  factory DriftNode.fromTailscaleDevice(Map<String, dynamic> device, int port) {
    final addresses = device['addresses'] as List<dynamic>? ?? [];
    final ipAddress = addresses.isNotEmpty ? addresses.first as String : '';
    
    return DriftNode(
      id: device['id'] as String,
      name: device['name'] as String,
      ipAddress: ipAddress,
      port: port,
      isOnline: device['online'] as bool? ?? false,
      lastSeen: DateTime.parse(device['lastSeen'] as String? ?? DateTime.now().toIso8601String()),
    );
  }

  /// Create from JSON
  factory DriftNode.from<PERSON>son(Map<String, dynamic> json) {
    return DriftNode(
      id: json['id'] as String,
      name: json['name'] as String,
      ipAddress: json['ip_address'] as String,
      port: json['port'] as int,
      isOnline: json['is_online'] as bool,
      lastSeen: DateTime.parse(json['last_seen'] as String),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'ip_address': ipAddress,
      'port': port,
      'is_online': isOnline,
      'last_seen': lastSeen.toIso8601String(),
    };
  }

  /// Get the base URL for this node's API
  String get baseUrl => 'http://$ipAddress:$port';

  /// Create a copy with updated fields
  DriftNode copyWith({
    String? id,
    String? name,
    String? ipAddress,
    int? port,
    bool? isOnline,
    DateTime? lastSeen,
  }) {
    return DriftNode(
      id: id ?? this.id,
      name: name ?? this.name,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  @override
  String toString() {
    return 'DriftNode(id: $id, name: $name, ipAddress: $ipAddress, '
        'port: $port, isOnline: $isOnline, lastSeen: $lastSeen)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DriftNode && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
