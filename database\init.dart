import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:sqflite/sqflite.dart';

/// Database initialization utilities
class DatabaseInit {
  static const String databaseName = 'drift.db';
  static const int currentVersion = 1;
  
  /// Initialize database with migrations
  static Future<Database> initialize(String databasePath) async {
    return await openDatabase(
      path.join(databasePath, databaseName),
      version: currentVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }
  
  /// Create database tables on first run
  static Future<void> _onCreate(Database db, int version) async {
    await _runMigration(db, 1);
  }
  
  /// Handle database upgrades
  static Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    for (int version = oldVersion + 1; version <= newVersion; version++) {
      await _runMigration(db, version);
    }
  }
  
  /// Run a specific migration
  static Future<void> _runMigration(Database db, int version) async {
    switch (version) {
      case 1:
        await _createInitialSchema(db);
        break;
      default:
        throw ArgumentError('Unknown migration version: $version');
    }
  }
  
  /// Create initial schema (v1)
  static Future<void> _createInitialSchema(Database db) async {
    // Create schema_migrations table first
    await db.execute('''
      CREATE TABLE IF NOT EXISTS schema_migrations (
        version INTEGER PRIMARY KEY,
        applied_at TEXT NOT NULL
      )
    ''');
    
    // Create events table
    await db.execute('''
      CREATE TABLE events (
        id TEXT PRIMARY KEY,
        origin TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        payload TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      )
    ''');
    
    // Create indexes for better query performance
    await db.execute('CREATE INDEX idx_events_origin ON events(origin)');
    await db.execute('CREATE INDEX idx_events_entity ON events(entity_type, entity_id)');
    await db.execute('CREATE INDEX idx_events_synced ON events(synced)');
    await db.execute('CREATE INDEX idx_events_timestamp ON events(timestamp)');
    
    // Record migration
    await db.insert('schema_migrations', {
      'version': 1,
      'applied_at': DateTime.now().toIso8601String(),
    });
  }
}
