import 'dart:async';
import 'dart:io';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import 'config/config.dart';
import 'database/database_manager.dart';
import 'api/drift_api.dart';
import 'sync/sync_manager.dart';

/// Main class that orchestrates the Drift system
class Core {

  
  late final DatabaseManager _databaseManager;
  late final DriftApi _api;
  late final SyncManager _syncManager;
  late final String _nodeId;
  
  bool _initialized = false;
  
  /// Get the unique node identifier
  String get nodeId => _nodeId;
  
  /// Get the database manager instance
  DatabaseManager get database => _databaseManager;
  
  /// Get the API instance
  DriftApi get api => _api;
  
  /// Get the sync manager instance
  SyncManager get sync => _syncManager;
  
  /// Initialize the Drift core system
  Future<void> initialize({
    String? customNodeId,
    int apiPort = Config.defaultApiPort,
    String? tailscaleAccessToken,
  }) async {
    if (_initialized) return;
    
    // Generate or retrieve node ID
    _nodeId = customNodeId ?? await _getOrCreateNodeId();
    
    // Initialize database
    _databaseManager = DatabaseManager();
    await _databaseManager.initialize();
    
    // Initialize API server
    _api = DriftApi(_databaseManager, _nodeId);
    await _api.start(port: apiPort);
    
    // Initialize sync manager
    _syncManager = SyncManager(
      databaseManager: _databaseManager,
      nodeId: _nodeId,
      apiPort: apiPort,
      tailscaleAccessToken: tailscaleAccessToken,
    );
    await _syncManager.initialize();
    
    _initialized = true;
  }
  
  /// Shutdown the Drift core system
  Future<void> shutdown() async {
    if (!_initialized) return;
    
    await _syncManager.shutdown();
    await _api.stop();
    await _databaseManager.close();
    
    _initialized = false;
  }
  
  /// Get or create a persistent node ID
  Future<String> _getOrCreateNodeId() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final nodeIdFile = File(path.join(directory.path, 'drift_node_id.txt'));
      
      if (await nodeIdFile.exists()) {
        final nodeId = await nodeIdFile.readAsString();
        if (nodeId.isNotEmpty) return nodeId.trim();
      }
      
      // Generate new node ID
      const uuid = Uuid();
      final newNodeId = uuid.v4();
      await nodeIdFile.writeAsString(newNodeId);
      
      return newNodeId;
    } catch (e) {
      // Fallback to memory-only UUID if file operations fail
      const uuid = Uuid();
      return uuid.v4();
    }
  }
}
