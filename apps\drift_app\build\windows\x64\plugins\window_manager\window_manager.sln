﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}"
	ProjectSection(ProjectDependencies) = postProject
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3} = {D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}
		{9931270E-0C88-32F9-AD1A-D959446FA444} = {9931270E-0C88-32F9-AD1A-D959446FA444}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{8936738C-070F-383C-89A1-E92794C59A33}"
	ProjectSection(ProjectDependencies) = postProject
		{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42} = {8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3} = {D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}"
	ProjectSection(ProjectDependencies) = postProject
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3} = {D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}"
	ProjectSection(ProjectDependencies) = postProject
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3} = {D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4} = {4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "window_manager_plugin", "window_manager_plugin.vcxproj", "{9931270E-0C88-32F9-AD1A-D959446FA444}"
	ProjectSection(ProjectDependencies) = postProject
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3} = {D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4} = {4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}
		{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574} = {3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}.Debug|x64.ActiveCfg = Debug|x64
		{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}.Debug|x64.Build.0 = Debug|x64
		{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}.Profile|x64.ActiveCfg = Profile|x64
		{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}.Profile|x64.Build.0 = Profile|x64
		{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}.Release|x64.ActiveCfg = Release|x64
		{8FF5FDCF-AA82-3AB9-8E72-CAD417AB5D42}.Release|x64.Build.0 = Release|x64
		{8936738C-070F-383C-89A1-E92794C59A33}.Debug|x64.ActiveCfg = Debug|x64
		{8936738C-070F-383C-89A1-E92794C59A33}.Profile|x64.ActiveCfg = Profile|x64
		{8936738C-070F-383C-89A1-E92794C59A33}.Release|x64.ActiveCfg = Release|x64
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}.Debug|x64.ActiveCfg = Debug|x64
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}.Debug|x64.Build.0 = Debug|x64
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}.Profile|x64.ActiveCfg = Profile|x64
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}.Profile|x64.Build.0 = Profile|x64
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}.Release|x64.ActiveCfg = Release|x64
		{D38C300B-ECD9-327C-A1D3-7F0B2A30FBF3}.Release|x64.Build.0 = Release|x64
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}.Debug|x64.ActiveCfg = Debug|x64
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}.Debug|x64.Build.0 = Debug|x64
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}.Profile|x64.ActiveCfg = Profile|x64
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}.Profile|x64.Build.0 = Profile|x64
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}.Release|x64.ActiveCfg = Release|x64
		{4B0EDB18-EFD8-3B88-A1F4-84799CE4D0F4}.Release|x64.Build.0 = Release|x64
		{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}.Debug|x64.ActiveCfg = Debug|x64
		{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}.Debug|x64.Build.0 = Debug|x64
		{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}.Profile|x64.ActiveCfg = Profile|x64
		{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}.Profile|x64.Build.0 = Profile|x64
		{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}.Release|x64.ActiveCfg = Release|x64
		{3B6FF80E-9BA0-30A6-BAAA-3FE14BFF9574}.Release|x64.Build.0 = Release|x64
		{9931270E-0C88-32F9-AD1A-D959446FA444}.Debug|x64.ActiveCfg = Debug|x64
		{9931270E-0C88-32F9-AD1A-D959446FA444}.Debug|x64.Build.0 = Debug|x64
		{9931270E-0C88-32F9-AD1A-D959446FA444}.Profile|x64.ActiveCfg = Profile|x64
		{9931270E-0C88-32F9-AD1A-D959446FA444}.Profile|x64.Build.0 = Profile|x64
		{9931270E-0C88-32F9-AD1A-D959446FA444}.Release|x64.ActiveCfg = Release|x64
		{9931270E-0C88-32F9-AD1A-D959446FA444}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E5AC5055-4CCC-37F5-8CDD-E055C999ACDD}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
