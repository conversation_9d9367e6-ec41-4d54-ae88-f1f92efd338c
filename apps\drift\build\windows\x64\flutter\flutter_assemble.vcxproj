﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C5F521ED-3961-3582-A32E-6739EA253FFF}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>flutter_assemble</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\CMakeFiles\66cd9a575134688857265fcfdc7fffee\flutter_windows.dll.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_windows.dll, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_export.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_windows.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_messenger.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_plugin_registrar.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_texture_registrar.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\sdk\flutter PROJECT_DIR=D:\code\drift\apps\drift FLUTTER_ROOT=D:\sdk\flutter FLUTTER_EPHEMERAL_DIR=D:\code\drift\apps\drift\windows\flutter\ephemeral PROJECT_DIR=D:\code\drift\apps\drift FLUTTER_TARGET=D:\code\drift\apps\drift\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\code\drift\apps\drift\.dart_tool\package_config.json D:/sdk/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.dll;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_export.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_messenger.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;D:\code\drift\apps\drift\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Generating D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_windows.dll, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_export.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_windows.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_messenger.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_plugin_registrar.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_texture_registrar.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\sdk\flutter PROJECT_DIR=D:\code\drift\apps\drift FLUTTER_ROOT=D:\sdk\flutter FLUTTER_EPHEMERAL_DIR=D:\code\drift\apps\drift\windows\flutter\ephemeral PROJECT_DIR=D:\code\drift\apps\drift FLUTTER_TARGET=D:\code\drift\apps\drift\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\code\drift\apps\drift\.dart_tool\package_config.json D:/sdk/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Profile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.dll;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_export.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_messenger.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;D:\code\drift\apps\drift\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_windows.dll, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_export.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_windows.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_messenger.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_plugin_registrar.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/flutter_texture_registrar.h, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, D:/code/drift/apps/drift/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\sdk\flutter PROJECT_DIR=D:\code\drift\apps\drift FLUTTER_ROOT=D:\sdk\flutter FLUTTER_EPHEMERAL_DIR=D:\code\drift\apps\drift\windows\flutter\ephemeral PROJECT_DIR=D:\code\drift\apps\drift FLUTTER_TARGET=D:\code\drift\apps\drift\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\code\drift\apps\drift\.dart_tool\package_config.json D:/sdk/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.dll;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_export.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_messenger.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;D:\code\drift\apps\drift\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\code\drift\apps\drift\build\windows\x64\CMakeFiles\6da70352a6b80fc49073a27d020deb28\flutter_assemble.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.dll;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_export.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_messenger.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\drift\apps\drift\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.dll;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_export.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_messenger.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\code\drift\apps\drift\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.dll;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_export.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_windows.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_messenger.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\code\drift\apps\drift\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\drift\apps\drift\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\code\drift\apps\drift\windows\flutter\CMakeLists.txt">
      <StdOutEncoding>UTF-8</StdOutEncoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/code/drift/apps/drift/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/drift/windows -BD:/code/drift/apps/drift/build/windows/x64 --check-stamp-file D:/code/drift/apps/drift/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\drift\apps\drift\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/code/drift/apps/drift/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/drift/windows -BD:/code/drift/apps/drift/build/windows/x64 --check-stamp-file D:/code/drift/apps/drift/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\code\drift\apps\drift\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/code/drift/apps/drift/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/drift/windows -BD:/code/drift/apps/drift/build/windows/x64 --check-stamp-file D:/code/drift/apps/drift/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\drift\apps\drift\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\drift\apps\drift\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\code\drift\apps\drift\build\windows\x64\flutter\CMakeFiles\flutter_assemble">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\code\drift\apps\drift\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{01BDB762-1ADC-3738-91D1-55A9175D1C97}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>