import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_cors_headers/shelf_cors_headers.dart';

import '../database/database_manager.dart';
import '../models/drift_event.dart';

/// HTTP API server for the Drift node
class DriftApi {
  final DatabaseManager _databaseManager;
  final String _nodeId;
  
  HttpServer? _server;
  Router? _router;
  
  DriftApi(this._databaseManager, this._nodeId);

  /// Start the API server
  Future<void> start({int port = 8080}) async {
    if (_server != null) return;
    
    _router = Router();
    _setupRoutes();
    
    final handler = Pipeline()
        .addMiddleware(corsHeaders())
        .addMiddleware(logRequests())
        .addHandler(_router!);
    
    _server = await shelf_io.serve(handler, InternetAddress.anyIPv4, port);
    print('Drift API server started on port ${_server!.port}');
  }

  /// Stop the API server
  Future<void> stop() async {
    await _server?.close();
    _server = null;
    _router = null;
  }

  /// Setup API routes
  void _setupRoutes() {
    _router!.get('/health', _healthCheck);
    _router!.get('/node', _getNodeInfo);
    _router!.get('/events', _getEvents);
    _router!.post('/events', _receiveEvents);
    _router!.get('/database', _getDatabaseFile);
    _router!.get('/tasks', _getTasks);
  }

  /// Health check endpoint
  Response _healthCheck(Request request) {
    return Response.ok(
      jsonEncode({
        'status': 'healthy',
        'node_id': _nodeId,
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  /// Get node information
  Response _getNodeInfo(Request request) {
    return Response.ok(
      jsonEncode({
        'node_id': _nodeId,
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  /// Get unsynced events
  Future<Response> _getEvents(Request request) async {
    try {
      final events = await _databaseManager.getUnsyncedEvents();
      final eventsJson = events.map((event) => event.toJson()).toList();
      
      return Response.ok(
        jsonEncode({
          'events': eventsJson,
          'count': events.length,
          'node_id': _nodeId,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get events: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Receive events from other nodes
  Future<Response> _receiveEvents(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      final eventsData = data['events'] as List<dynamic>;
      final events = eventsData
          .map((eventData) => DriftEvent.fromJson(eventData as Map<String, dynamic>))
          .toList();
      
      // Filter out events that originated from this node
      final externalEvents = events.where((event) => event.origin != _nodeId).toList();
      
      if (externalEvents.isNotEmpty) {
        await _databaseManager.insertEvents(externalEvents);
      }
      
      return Response.ok(
        jsonEncode({
          'received': externalEvents.length,
          'filtered': events.length - externalEvents.length,
          'status': 'success',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.badRequest(
        body: jsonEncode({'error': 'Failed to receive events: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get database file for sharing
  Future<Response> _getDatabaseFile(Request request) async {
    try {
      final dbPath = await _databaseManager.getDatabasePath();
      final file = File(dbPath);
      
      if (!await file.exists()) {
        return Response.notFound(
          jsonEncode({'error': 'Database file not found'}),
          headers: {'Content-Type': 'application/json'},
        );
      }
      
      final bytes = await file.readAsBytes();
      return Response.ok(
        bytes,
        headers: {
          'Content-Type': 'application/octet-stream',
          'Content-Disposition': 'attachment; filename="drift.db"',
        },
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get database file: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Get current tasks (reconstructed from events)
  Future<Response> _getTasks(Request request) async {
    try {
      // This is a simplified version - in a real implementation,
      // you'd use the EventStore to reconstruct task states
      final allEvents = await _databaseManager.getAllEvents();
      final taskEvents = allEvents.where((event) => event.entityType == 'task').toList();
      
      // Group by entity_id and reconstruct states
      final Map<String, List<DriftEvent>> taskGroups = {};
      for (final event in taskEvents) {
        taskGroups.putIfAbsent(event.entityId, () => []).add(event);
      }
      
      final List<Map<String, dynamic>> tasks = [];
      for (final entry in taskGroups.entries) {
        final taskState = _reconstructTaskState(entry.key, entry.value);
        if (taskState != null) {
          tasks.add(taskState);
        }
      }
      
      return Response.ok(
        jsonEncode({
          'tasks': tasks,
          'count': tasks.length,
          'node_id': _nodeId,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      return Response.internalServerError(
        body: jsonEncode({'error': 'Failed to get tasks: $e'}),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Reconstruct task state from events (simplified version)
  Map<String, dynamic>? _reconstructTaskState(String taskId, List<DriftEvent> events) {
    if (events.isEmpty) return null;
    
    // Sort events by timestamp
    events.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    Map<String, dynamic> state = {};
    
    for (final event in events) {
      switch (event.eventType) {
        case 'task_created':
          state = {
            'id': taskId,
            'title': event.payload['title'],
            'description': event.payload['description'] ?? '',
            'completed': false,
            'created_at': event.timestamp.toIso8601String(),
            'updated_at': event.timestamp.toIso8601String(),
          };
          break;
          
        case 'task_completed':
          state['completed'] = true;
          state['completed_at'] = event.timestamp.toIso8601String();
          state['updated_at'] = event.timestamp.toIso8601String();
          break;
          
        case 'task_deleted':
          return null; // Task was deleted
      }
    }
    
    return state.isEmpty ? null : state;
  }
}
