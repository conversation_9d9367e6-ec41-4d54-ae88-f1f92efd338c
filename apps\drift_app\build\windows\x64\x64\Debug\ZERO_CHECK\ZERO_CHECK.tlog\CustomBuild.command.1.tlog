^D:\CODE\DRIFT\APPS\DRIFT_APP\BUILD\WINDOWS\X64\CMAKEFILES\8FC53E5A91EB2FB6114A8E8A9CE6A4BC\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/drift_app/windows -BD:/code/drift/apps/drift_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/drift/apps/drift_app/build/windows/x64/drift_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
