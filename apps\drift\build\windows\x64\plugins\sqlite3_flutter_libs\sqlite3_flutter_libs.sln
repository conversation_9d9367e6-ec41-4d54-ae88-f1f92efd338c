﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{3325A433-85C8-3C72-AB5F-A96EABAAF526}"
	ProjectSection(ProjectDependencies) = postProject
		{01BDB762-1ADC-3738-91D1-55A9175D1C97} = {01BDB762-1ADC-3738-91D1-55A9175D1C97}
		{C2169529-C675-3A20-A99F-BF71A327A0F9} = {C2169529-C675-3A20-A99F-BF71A327A0F9}
		{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A} = {BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{C30CFDA0-64C7-3353-9A5C-259C149ACC43}"
	ProjectSection(ProjectDependencies) = postProject
		{3325A433-85C8-3C72-AB5F-A96EABAAF526} = {3325A433-85C8-3C72-AB5F-A96EABAAF526}
		{01BDB762-1ADC-3738-91D1-55A9175D1C97} = {01BDB762-1ADC-3738-91D1-55A9175D1C97}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{01BDB762-1ADC-3738-91D1-55A9175D1C97}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{C5F521ED-3961-3582-A32E-6739EA253FFF}"
	ProjectSection(ProjectDependencies) = postProject
		{01BDB762-1ADC-3738-91D1-55A9175D1C97} = {01BDB762-1ADC-3738-91D1-55A9175D1C97}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{F9390FA6-9193-3410-B431-2E2958E622FD}"
	ProjectSection(ProjectDependencies) = postProject
		{01BDB762-1ADC-3738-91D1-55A9175D1C97} = {01BDB762-1ADC-3738-91D1-55A9175D1C97}
		{C5F521ED-3961-3582-A32E-6739EA253FFF} = {C5F521ED-3961-3582-A32E-6739EA253FFF}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sqlite3", "sqlite3.vcxproj", "{C2169529-C675-3A20-A99F-BF71A327A0F9}"
	ProjectSection(ProjectDependencies) = postProject
		{01BDB762-1ADC-3738-91D1-55A9175D1C97} = {01BDB762-1ADC-3738-91D1-55A9175D1C97}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sqlite3_flutter_libs_plugin", "sqlite3_flutter_libs_plugin.vcxproj", "{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}"
	ProjectSection(ProjectDependencies) = postProject
		{01BDB762-1ADC-3738-91D1-55A9175D1C97} = {01BDB762-1ADC-3738-91D1-55A9175D1C97}
		{C5F521ED-3961-3582-A32E-6739EA253FFF} = {C5F521ED-3961-3582-A32E-6739EA253FFF}
		{F9390FA6-9193-3410-B431-2E2958E622FD} = {F9390FA6-9193-3410-B431-2E2958E622FD}
		{C2169529-C675-3A20-A99F-BF71A327A0F9} = {C2169529-C675-3A20-A99F-BF71A327A0F9}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3325A433-85C8-3C72-AB5F-A96EABAAF526}.Debug|x64.ActiveCfg = Debug|x64
		{3325A433-85C8-3C72-AB5F-A96EABAAF526}.Debug|x64.Build.0 = Debug|x64
		{3325A433-85C8-3C72-AB5F-A96EABAAF526}.Profile|x64.ActiveCfg = Profile|x64
		{3325A433-85C8-3C72-AB5F-A96EABAAF526}.Profile|x64.Build.0 = Profile|x64
		{3325A433-85C8-3C72-AB5F-A96EABAAF526}.Release|x64.ActiveCfg = Release|x64
		{3325A433-85C8-3C72-AB5F-A96EABAAF526}.Release|x64.Build.0 = Release|x64
		{C30CFDA0-64C7-3353-9A5C-259C149ACC43}.Debug|x64.ActiveCfg = Debug|x64
		{C30CFDA0-64C7-3353-9A5C-259C149ACC43}.Profile|x64.ActiveCfg = Profile|x64
		{C30CFDA0-64C7-3353-9A5C-259C149ACC43}.Release|x64.ActiveCfg = Release|x64
		{01BDB762-1ADC-3738-91D1-55A9175D1C97}.Debug|x64.ActiveCfg = Debug|x64
		{01BDB762-1ADC-3738-91D1-55A9175D1C97}.Debug|x64.Build.0 = Debug|x64
		{01BDB762-1ADC-3738-91D1-55A9175D1C97}.Profile|x64.ActiveCfg = Profile|x64
		{01BDB762-1ADC-3738-91D1-55A9175D1C97}.Profile|x64.Build.0 = Profile|x64
		{01BDB762-1ADC-3738-91D1-55A9175D1C97}.Release|x64.ActiveCfg = Release|x64
		{01BDB762-1ADC-3738-91D1-55A9175D1C97}.Release|x64.Build.0 = Release|x64
		{C5F521ED-3961-3582-A32E-6739EA253FFF}.Debug|x64.ActiveCfg = Debug|x64
		{C5F521ED-3961-3582-A32E-6739EA253FFF}.Debug|x64.Build.0 = Debug|x64
		{C5F521ED-3961-3582-A32E-6739EA253FFF}.Profile|x64.ActiveCfg = Profile|x64
		{C5F521ED-3961-3582-A32E-6739EA253FFF}.Profile|x64.Build.0 = Profile|x64
		{C5F521ED-3961-3582-A32E-6739EA253FFF}.Release|x64.ActiveCfg = Release|x64
		{C5F521ED-3961-3582-A32E-6739EA253FFF}.Release|x64.Build.0 = Release|x64
		{F9390FA6-9193-3410-B431-2E2958E622FD}.Debug|x64.ActiveCfg = Debug|x64
		{F9390FA6-9193-3410-B431-2E2958E622FD}.Debug|x64.Build.0 = Debug|x64
		{F9390FA6-9193-3410-B431-2E2958E622FD}.Profile|x64.ActiveCfg = Profile|x64
		{F9390FA6-9193-3410-B431-2E2958E622FD}.Profile|x64.Build.0 = Profile|x64
		{F9390FA6-9193-3410-B431-2E2958E622FD}.Release|x64.ActiveCfg = Release|x64
		{F9390FA6-9193-3410-B431-2E2958E622FD}.Release|x64.Build.0 = Release|x64
		{C2169529-C675-3A20-A99F-BF71A327A0F9}.Debug|x64.ActiveCfg = Debug|x64
		{C2169529-C675-3A20-A99F-BF71A327A0F9}.Debug|x64.Build.0 = Debug|x64
		{C2169529-C675-3A20-A99F-BF71A327A0F9}.Profile|x64.ActiveCfg = Profile|x64
		{C2169529-C675-3A20-A99F-BF71A327A0F9}.Profile|x64.Build.0 = Profile|x64
		{C2169529-C675-3A20-A99F-BF71A327A0F9}.Release|x64.ActiveCfg = Release|x64
		{C2169529-C675-3A20-A99F-BF71A327A0F9}.Release|x64.Build.0 = Release|x64
		{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}.Debug|x64.ActiveCfg = Debug|x64
		{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}.Debug|x64.Build.0 = Debug|x64
		{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}.Profile|x64.ActiveCfg = Profile|x64
		{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}.Profile|x64.Build.0 = Profile|x64
		{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}.Release|x64.ActiveCfg = Release|x64
		{BFCAEC41-10E5-3DEF-8B50-7E9A85FB164A}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F3EF12A2-AC48-34BD-A2DB-9CE1CD4B04C0}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
